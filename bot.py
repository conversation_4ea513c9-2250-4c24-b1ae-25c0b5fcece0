import os
import logging
import json
import subprocess
from telegram import Update, <PERSON>lyKeyboardMarkup, KeyboardButton, <PERSON>lyKeyboardRemove
from telegram.ext import <PERSON>dater, CommandHandler, MessageHandler, Filters, CallbackContext
from dotenv import load_dotenv
import google.generativeai as genai

# --- Setup ---
load_dotenv()
TELEGRAM_TOKEN = os.getenv("TELEGRAM_TOKEN")
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")

logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s", level=logging.INFO
)
logger = logging.getLogger(__name__)

try:
    genai.configure(api_key=GEMINI_API_KEY)
    gemini_model = genai.GenerativeModel('gemini-pro')
except Exception as e:
    logger.error(f"Failed to configure Gemini: {e}")
    gemini_model = None

# --- Master Prompt for Gemini ---
MASTER_PROMPT = """
You are an expert ethical hacking and penetration testing assistant named 'Sayer'. 
Your primary function is to analyze a user's request and create a concise, step-by-step action plan.

**Constraints & Rules:**
1.  **Safety First:** The plan must be strictly for reconnaissance, information gathering, and non-destructive vulnerability scanning. Do NOT include any commands that could cause harm, such as exploits, DDoS attacks, or deleting files.
2.  **Tool-Based:** You must use only the following Kali Linux tools: `nmap`, `whois`, `dig`, `whatweb`, `nikto`, `subfinder`.
3.  **Output Format:** Your output MUST be a valid, raw JSON object and nothing else. Do not wrap it in markdown or any other text.
4.  **Plan Structure:** The JSON must have a single key "plan" which is a list of objects. Each object must have two keys: "description" (a brief, clear explanation of the step in Arabic) and "command" (the exact shell command to execute).
5.  **Dynamic & Relevant:** The plan should be tailored to the user's specific request.

**User Request:** "{user_request}"

**JSON Output Example:**
{
    "plan": [
        {"description": "البحث عن معلومات تسجيل النطاق", "command": "whois example.com"},
        {"description": "البحث عن النطاقات الفرعية", "command": "subfinder -d example.com"}
    ]
}

Now, generate the plan for the user's request.
"""

# --- Gemini Planner ---
def generate_plan_with_gemini(user_request: str) -> list[dict] | None:
    if not gemini_model:
        logger.error("Gemini model is not available.")
        return None

    prompt = MASTER_PROMPT.format(user_request=user_request)
    
    try:
        logger.info("Sending request to Gemini API...")
        response = gemini_model.generate_content(prompt)
        cleaned_text = response.text.strip().replace("```json", "").replace("```", "").strip()
        logger.info(f"Received from Gemini: {cleaned_text}")
        json_response = json.loads(cleaned_text)
        return json_response.get("plan")
    except Exception as e:
        logger.error(f"Error generating plan with Gemini: {e}")
        logger.error(f"Gemini's raw response was: {response.text if 'response' in locals() else 'N/A'}")
        return None

# --- Executor ---
def execute_command(command: str) -> str:
    command_to_run = command
    if 'subfinder' in command and os.geteuid() == 0:
        home_dir = os.path.expanduser('~')
        command_to_run = f"HOME={home_dir} {command}"

    try:
        logger.info(f"Executing command: {command_to_run}")
        process = subprocess.run(
            command_to_run, shell=True, capture_output=True, text=True, timeout=300
        )
        output = process.stdout + process.stderr
        return output if output else "(No output)"
    except subprocess.TimeoutExpired:
        return "Error: Command timed out after 5 minutes."
    except Exception as e:
        logger.error(f"Failed to execute command '{command_to_run}': {e}")
        return f"An exception occurred: {e}"

# --- Telegram Handlers (v13.x syntax) ---
def start(update: Update, context: CallbackContext) -> None:
    update.message.reply_text(
        "أهلاً بك في Sayer، وكيل اختبار الاختراق الخاص بك.\n"
        "فقط أخبرني باللغة الطبيعية ماذا تريد أن تفحص.\n\n"
        "مثال: `افحص موقع example.com وابحث عن النطاقات الفرعية`\n\n"
        "**تنبيه:** هذه الأداة للأغراض التعليمية والأخلاقية فقط."
    )

def handle_message(update: Update, context: CallbackContext) -> None:
    user_message = update.message.text
    logger.info(f"Received message from {update.effective_user.first_name}: {user_message}")

    update.message.reply_text("تمام، أقوم بتحليل طلبك وتجهيز الخطة باستخدام الذكاء الاصطناعي...")

    plan = generate_plan_with_gemini(user_message)
    
    if not plan:
        update.message.reply_text("عذراً، لم أتمكن من إنشاء خطة. قد تكون هناك مشكلة في الاتصال بالنموذج اللغوي أو أن الطلب غير واضح. حاول مرة أخرى.")
        return
        
    context.user_data['plan'] = plan
    
    plan_text = "خطة العمل المقترحة جاهزة:\n\n"
    for i, step in enumerate(plan):
        plan_text += f"*{i+1}. {step['description']}*\n"
        plan_text += f"`{step['command']}`\n\n"
    
    plan_text += "هل أبدأ التنفيذ؟"

    keyboard = [[KeyboardButton("نعم، ابدأ التنفيذ"), KeyboardButton("لا، الغِ الخطة")]]
    reply_markup = ReplyKeyboardMarkup(keyboard, one_time_keyboard=True, resize_keyboard=True)
    
    update.message.reply_text(plan_text, reply_markup=reply_markup, parse_mode='Markdown')

def handle_response(update: Update, context: CallbackContext) -> None:
    response = update.message.text
    
    if 'plan' not in context.user_data:
        update.message.reply_text("لا توجد خطة قيد الانتظار. ابدأ بطلب جديد.", reply_markup=ReplyKeyboardRemove())
        return

    if "نعم" in response:
        update.message.reply_text("تمام، سأبدأ التنفيذ الآن...", reply_markup=ReplyKeyboardRemove())
        
        plan = context.user_data['plan']
        total_steps = len(plan)
        
        for i, step in enumerate(plan):
            update.message.reply_text(f"--- *الخطوة {i+1}/{total_steps}:* {step['description']} ---", parse_mode='Markdown')
            result = execute_command(step['command'])
            
            if len(result) > 4000:
                for x in range(0, len(result), 4000):
                    update.message.reply_text(f"```\n{result[x:x+4000]}\n```", parse_mode='Markdown')
            else:
                update.message.reply_text(f"```\n{result}\n```", parse_mode='Markdown')

        update.message.reply_text("--- انتهى تنفيذ الخطة بالكامل. ---")

    else:
        update.message.reply_text("تم إلغاء الخطة.", reply_markup=ReplyKeyboardRemove())
        
    del context.user_data['plan']

def main() -> None:
    if not TELEGRAM_TOKEN or not GEMINI_API_KEY:
        logger.error("FATAL: TELEGRAM_TOKEN or GEMINI_API_KEY are not set in .env file!")
        return
    if not gemini_model:
        logger.error("FATAL: Gemini model could not be initialized. Check API key and configuration.")
        return

    updater = Updater(TELEGRAM_TOKEN, use_context=True)
    dispatcher = updater.dispatcher

    dispatcher.add_handler(CommandHandler("start", start))
    dispatcher.add_handler(MessageHandler(Filters.regex(r'^(نعم|لا)'), handle_response))
    dispatcher.add_handler(MessageHandler(Filters.text & ~Filters.command, handle_message))

    logger.info("Sayer Bot is starting with v13.15 library...")
    updater.start_polling()
    updater.idle()

if __name__ == "__main__":
    main()